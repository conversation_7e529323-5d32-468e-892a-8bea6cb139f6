import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ambulance } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const services = [
  {
    title: "Cardiology",
    description: "Expert heart care with advanced diagnostic and treatment options",
    icon: Heart,
  },
  {
    title: "Neurology",
    description: "Comprehensive care for neurological conditions and disorders",
    icon: Brain,
  },
  {
    title: "Primary Care",
    description: "Preventive care and routine check-ups for your overall health",
    icon: Stethoscope,
  },
  {
    title: "Emergency Care",
    description: "24/7 emergency services with rapid response times",
    icon: Ambulance,
  },
];

export const ServicesSection = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6 animate-fade-up">
        <h2 className="text-3xl font-bold text-center text-hospital-blue mb-12">
          Our Services
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service) => (
            <Card key={service.title} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <service.icon className="h-12 w-12 text-hospital-green mb-4" />
                <CardTitle>{service.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};