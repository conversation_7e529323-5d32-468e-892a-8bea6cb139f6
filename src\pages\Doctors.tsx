import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const doctors = [
  {
    name: "Dr. <PERSON><PERSON>",
    specialty: "MBBS, DNB (Ortho)",
    description: "Joint Replacement & Reconstruction Surgeries",
    image: "/doctors/dombale.jpg"
  },
  {
    name: "Dr. <PERSON>",
    specialty: "MBBS, MD (Med).",
    description: "Specializes in Medicine",
    image: "/doctors/kute.jpeg"
  },
  {
    name: "Dr. <PERSON><PERSON>",
    specialty: "MBBS, M.S., D.N.B. (OBGY) MRCOG (London)",
    description: "Laparoscopy, In Vitro Fertilisation (IVF)",
    image: "/doctors/anjikar.jpeg"
  },
  {
    name: "Dr. <PERSON><PERSON><PERSON>",
    specialty: "MBBS, MS (ENT)",
    description: "Endoscopic ENT Surgeon",
    image: "/doctors/deshmukh.jpg"
  },
  {
    name: "Dr. <PERSON><PERSON>",
    specialty: "MBBS, MS (Ortho)",
    description: "Spine surgeon",
    image: "/doctors/gmundhe.jpg"
  },
  {
    name: "Dr. <PERSON><PERSON>",
    specialty: "MB<PERSON>, DNB (Nephro)",
    description: "Nephrologist, Renal Replacement and Transplant, Renal Medicine",
    image: "/doctors/khot.png"
  },
  {
    name: "Dr. Amit Yele",
    specialty: "MBBS, MD (Med), DNB (Med)",
    description: "Diabetologist, Rheumatologist, Physician and Intensive care",
    image: "/doctors/yele.jpg"
  },
  {
    name: "Dr. Sachin Naik",
    specialty: "MBBS, MS (Gen Surgery), D. Lap. FMAS",
    description: "Open and Laparoscopic Surgeries",
    image: "/doctors/naik.jpg"
  },
  {
    name: "Dr. Devendra Satpute",
    specialty: "MBBS, DCH.",
    description: "Paediatrician and Neonetologist",
    image: "/doctors/satpute.jpg"
  },
  {
    name: "Dr. Shankar Mundhe",
    specialty: "MBBS, MS (Gen Surgery), MCH (Urology)",
    description: "Urologist & Uro Surgeon",
    image: "/doctors/mundhe.jpg"
  },
  {
    name: "Dr. Nandkishor Raut",
    specialty: "MBBS, MS (Urology)",
    description: "Uro-Surgeon",
    image: "/doctors/raut.jpg"
  },
  {
    name: "Dr. Ashish Ubhale",
    specialty: "MBBS, MD (Psy.)",
    description: "Consultant Medical Psychiatrist and Psychotherapist",
    image: "/doctors/ubhale.jpg"
  },
  {
    name: "Dr. Sachin Vitnor",
    specialty: "MBBS, MS (Gynec-Obs)",
    description: "Gynaecologist, Obstretic Surgeon",
    image: "/doctors/vitnor.jpg"
  },
  {
    name: "Dr. Amol Walke",
    specialty: "MBBS, DOMS",
    description: "Eye Specialist",
    image: "/doctors/walke.jpg"
  },
  {
    name: "Dr. Amit Morey",
    specialty: "MBBS, DNB (ENT)",
    description: "Advanced Endoscopic Procedures",
    image: "/doctors/ENT.jpg"
  }
];

const Doctors = () => {
  return (
    <div className="container mx-auto px-4 py-8 animate-fade-up">
      <h1 className="text-4xl font-bold text-hospital-blue mb-8">Our Doctors</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {doctors.map((doctor, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow bg-gray-50">
            <CardHeader className="flex flex-row items-center gap-4">
              <Avatar className="w-24 h-24 rounded-lg !overflow-hidden">
                <AvatarImage src={doctor.image} alt={doctor.name} className="object-cover" />
                <AvatarFallback>{doctor.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle>{doctor.name}</CardTitle>
                <br/>
                <CardDescription className="text-hospital-blue font-medium">
                  {doctor.specialty}
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>{doctor.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
      <br/>
      <br/>
      <p className="text-lg text-gray-700 mb-8">
            Call us @ +91-772-202-6440 / +91-932-288-7125
      </p>
    </div>
  );
};

export default Doctors;
