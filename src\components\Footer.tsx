import { Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Twitter } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="bg-hospital-blue text-white mt-20">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">Harneshwar Multispeciality Hospital</h3>
            <p className="text-gray-300">
              Providing compassionate care and healing for over two decades.
            </p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><Link to="/departments" className="hover:text-gray-300">Departments</Link></li>
              <li><Link to="/doctors" className="hover:text-gray-300">Doctors</Link></li>
              <li><Link to="/facilities" className="hover:text-gray-300">Facilities</Link></li>
              <li><Link to="/gallery" className="hover:text-gray-300">Gallery</Link></li>
              <li><Link to="/about" className="hover:text-gray-300">About Us</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
            <ul className="space-y-2">
              <li className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                50,51 Harneshwar Society, Talegaon-Chakan Road, Talegaon Dabhade, Tal. Maval, Pune: 410507
              </li>
              <li className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                +91-************ / +91-************
              </li>
              <li className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <EMAIL>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Follow Us @</h4>
            <div className="flex space-x-4">
              <a href="https://www.facebook.com/profile.php?id=100073294189190" className="hover:text-gray-300"><Facebook className="h-6 w-6" /></a>
              {/* <a href="#" className="hover:text-gray-300"><Twitter className="h-6 w-6" /></a>
              <a href="#" className="hover:text-gray-300"><Instagram className="h-6 w-6" /></a>
              <a href="#" className="hover:text-gray-300"><Linkedin className="h-6 w-6" /></a> */}
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-600 mt-8 pt-8 text-center">
          <p>&copy; {new Date().getFullYear()} Harneshwar Multispeciality Hospital. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;