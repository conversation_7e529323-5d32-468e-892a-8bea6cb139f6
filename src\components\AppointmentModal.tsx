import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const departments = [
  "Cardiology",
  "Neurology",
  "Pediatrics",
  "Orthopedics",
  "Internal Medicine",
  "Diagnostic Services"
];

const AppointmentModal = ({ isOpen, onClose }: AppointmentModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    date: "",
    department: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Test Supabase connection when component mounts
  useEffect(() => {
    const testConnection = async () => {
      try {
        const { error } = await supabase.from("appointments").select("count").limit(1);
        if (error) {
          console.error("Supabase connection test failed:", error);
        } else {
          console.log("Supabase connection test successful");
        }
      } catch (err) {
        console.error("Supabase connection error:", err);
      }
    };

    if (isOpen) {
      testConnection();
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.name.trim()) {
        throw new Error("Full name is required");
      }
      if (!formData.phone.trim()) {
        throw new Error("Phone number is required");
      }
      if (!formData.date) {
        throw new Error("Date is required");
      }
      if (!formData.department) {
        throw new Error("Department is required");
      }

      // Prepare the data for insertion
      const appointmentData = {
        full_name: formData.name.trim(),
        email: formData.email.trim() || null, // Convert empty string to null
        phone: formData.phone.trim(),
        date: formData.date,
        department: formData.department,
      };

      console.log("Submitting appointment data:", appointmentData);

      const { data, error } = await supabase
        .from("appointments")
        .insert([appointmentData])
        .select(); // Add select to return the inserted data

      if (error) {
        console.error("Supabase error details:", error);
        throw error;
      }

      console.log("Appointment created successfully:", data);

      toast({
        title: "Appointment Scheduled",
        description: "We'll contact you shortly to confirm your appointment.",
      });

      // Reset form after successful submission
      setFormData({
        name: "",
        email: "",
        phone: "",
        date: "",
        department: "",
      });

      onClose();
    } catch (error: any) {
      console.error("Error scheduling appointment:", error);

      // Provide more specific error messages
      let errorMessage = "There was a problem scheduling your appointment. Please try again.";

      if (error.message) {
        if (error.message.includes("duplicate key") || error.message.includes("unique constraint")) {
          errorMessage = "An appointment with these details already exists. Please check your information.";
        } else if (error.message.includes("required") || error.message.includes("not null")) {
          errorMessage = "Please fill in all required fields.";
        } else if (error.message.includes("invalid") || error.message.includes("format")) {
          errorMessage = "Please check that all information is entered correctly.";
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDepartmentChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      department: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Book Appointment</DialogTitle>
          <DialogDescription>
            Fill in your details below to schedule an appointment with our medical team.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                // required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="date">Preferred Date</Label>
              <Input
                id="date"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleChange}
                min={new Date().toISOString().split('T')[0]} // Prevent past dates
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="department">Department</Label>
              <Select onValueChange={handleDepartmentChange} value={formData.department} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select a department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Scheduling..." : "Schedule Appointment"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AppointmentModal;