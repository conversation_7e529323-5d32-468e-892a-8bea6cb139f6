
import { Button } from "@/components/ui/button";
import { Calendar, Phone } from "lucide-react";
import { useState, useEffect } from "react";
import AppointmentModal from "./AppointmentModal";
import { useIsMobile } from "@/hooks/use-mobile";

export const HeroSection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const isMobile = useIsMobile();

  const handlePhoneCall = () => {
    window.location.href = "tel:+917722026440";
  };

  return (
    <section className="relative bg-hospital-light-blue min-h-[600px] flex items-center">
      <div className="container mx-auto px-6 py-20 flex flex-col md:flex-row items-center justify-between gap-8">
        <div className="max-w-2xl animate-fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-hospital-blue mb-6">
            Compassionate Care for Your Health Journey
          </h1>
          <p className="text-lg text-gray-700 mb-4">
            Experience world-class healthcare with our team of dedicated professionals. Your health and comfort are our top priorities.
          </p>
          <p className="text-lg text-gray-700 mb-4">
            Call us @ +91-************ / +91-************
          </p>
          
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={handlePhoneCall}
              disabled={!isMobile}
              className={`bg-green-600 hover:bg-green-700 ${!isMobile ? 'cursor-not-allowed opacity-50' : ''}`}
            >
              <Phone className="mr-2 h-5 w-5" />
              Call Now!
            </Button>
            
            <Button 
              size="lg" 
              className="bg-hospital-blue hover:bg-blue-700"
              onClick={() => setIsModalOpen(true)}
            >
              <Calendar className="mr-2 h-5 w-5" />
              Book Appointment
            </Button>
          </div>
        </div>
        <div className="w-full md:w-1/2 animate-fade-up">
          <img 
            src="/hospital/hosp3.jpg" 
            alt="Hospital Building" 
            className="rounded-lg shadow-xl w-full h-auto object-cover"
          />
        </div>
      </div>
      <AppointmentModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </section>
  );
};
