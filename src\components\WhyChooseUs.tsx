import { Shield, Clock, Award } from "lucide-react";

const reasons = [
  {
    title: "Expert Medical Team",
    description: "Board-certified physicians and experienced healthcare professionals",
    icon: Shield,
  },
  {
    title: "24/7 Availability",
    description: "Round-the-clock medical care and emergency services",
    icon: Clock,
  },
  {
    title: "Award-Winning Care",
    description: "Recognized excellence in patient care and safety",
    icon: Award,
  },
];

export const WhyChooseUs = () => {
  return (
    <section className="py-20 bg-hospital-light-blue">
      <div className="container mx-auto px-6 animate-fade-up">
        <h2 className="text-3xl font-bold text-center text-hospital-blue mb-12">
          Why Choose Us
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {reasons.map((reason) => (
            <div key={reason.title} className="text-center">
              <reason.icon className="h-16 w-16 mx-auto text-hospital-blue mb-4" />
              <h3 className="text-xl font-semibold mb-2">{reason.title}</h3>
              <p className="text-gray-600">{reason.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};