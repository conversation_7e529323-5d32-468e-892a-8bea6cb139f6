const About = () => {
  return (
    <div className="container mx-auto px-4 py-8 animate-fade-up">
      <h1 className="text-4xl font-bold text-hospital-blue mb-8">About Us</h1>
      <div className="prose max-w-none">
        <p className="text-lg">
          Welcome to Harneshwar Multispeciality Hospital, where we combine advanced medical expertise with compassionate care.
        </p>
        <br/>
        <p className="text-lg">
          Harneshwar Multispeciality Hospital is one of the oldest hospitals in the Talegaon-Maval region, established in 1989.
          Over the years, we have grown to become a premier healthcare facility, offering a wide range of medical services to our Mavalites.
          We have now transformed into a major healthcare hub, with multiple specialties and superspecialties.
        </p>
        
        <div className="my-12">
          <h2 className="text-3xl font-bold text-hospital-blue mb-6">Our Founders</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden">
                <img
                  src="/doctors/founder1.jpg"
                  alt="Dr. Founder"
                  className="w-full h-full object-scale-down"
                />
              </div>
              <h3 className="text-xl font-semibold text-hospital-blue">Dr. Shrikant Jategaonkar</h3>
              <p className="text-gray-700">
                Dr. Shrikant Jategaonkar, a visionary in healthcare, founded Harneshwar Multispeciality Hospital with the dream of providing 
                world-class medical care to the Talegaon-Maval region. His dedication to excellence and patient care has been the 
                cornerstone of our hospital's growth and success.
              </p>
            </div>
            <div className="space-y-4">
              <div className="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden">
                <img
                  src="/doctors/founder2.jpg"
                  alt="Co-Founder"
                  className="w-full h-full object-scale-down"
                />
              </div>
              <h3 className="text-xl font-semibold text-hospital-blue">Dr. Jyoti Jategaonkar</h3>
              <p className="text-gray-700">
                Dr. Jyoti Jategaonkar, co-founder of Harneshwar Multispeciality Hospital, has been instrumental in shaping our 
                hospital's patient-centric approach. Her expertise and commitment to healthcare excellence have helped establish 
                our hospital as a trusted name in medical care.
              </p>
            </div>
          </div>
        </div>

        <br/>
        <p className="text-lg">
          Our facility has been designed according to NABH guidelines, putting in consideration patient's safety first. 
          Special attention is given to fire safety norms, radiation safety, adequate light, ventilation, wide corridors and stair cases, power backup, 
          stretcher lift, security and ample parking space.
        </p>
        <br/>
        <p className="text-lg">
          We believe that a clean, healthy hospital environment facilitates patient's recovery as well as prevents spread of contagious diseases. 
          We follow proper biomedical waste management, CSSD, infection control protocols, cleaning of waiting rooms, lobbies, 
          rest rooms with special attention to frequently touched surfaces including door knobs, armrests and hand rails.
          Patient examination rooms are routinely sanitized with 360° rotating UV lights. Our experts monitor the latest guidelines published by state & National health authorities 
          and adapt our processes to follow them, thereby limiting the exposure of disease. 
        </p>
        <br/>
        <p className="text-lg">
          Keeping in mind WHO motto 'Save our Planet's Health' We have made our project Green Energy compliant by installing solar grid system of 20kW & contributing to water saving initiative by implementing rain water harvesting system.
        </p>
        <br/>
        <br/>
        <p className="text-lg text-gray-700 mb-8">
              Call us @ +91-************ / +91-************
        </p>
      </div>
    </div>
  );
};

export default About;