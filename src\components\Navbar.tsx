
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from "@/components/ui/navigation-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { Link } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState } from "react";

const Navbar = () => {
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    // Removed the Home option as requested
    { title: "Departments", href: "/departments" },
    { title: "Doctors", href: "/doctors" },
    { title: "Facilities", href: "/facilities" },
    { title: "Gallery", href: "/gallery" },
    { title: "About Us", href: "/about" },
  ];

  const NavLinks = () => (
    <>
      {navItems.map((item) => (
        <NavigationMenuItem key={item.title}>
          <Link
            to={item.href}
            className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium transition-colors hover:bg-white/20 hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50"
          >
            {item.title}
          </Link>
        </NavigationMenuItem>
      ))}
    </>
  );

  const MobileMenu = () => (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger className="p-2">
        <Menu className="h-6 w-6" />
      </SheetTrigger>
      <SheetContent side="left" className="w-[240px] sm:w-[280px]">
        <nav className="flex flex-col gap-4 mt-4">
          {navItems.map((item) => (
            <Link
              key={item.title}
              to={item.href}
              className="block px-2 py-1 text-lg hover:text-hospital-blue transition-colors"
              onClick={() => setIsOpen(false)}
            >
              {item.title}
            </Link>
          ))}
        </nav>
      </SheetContent>
    </Sheet>
  );

  return (
    <div className="w-full bg-gray-300/70 backdrop-blur-md shadow-sm fixed top-0 left-0 z-50 transition-all duration-300">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <Link to="/" className="flex items-center gap-2 text-hospital-blue">
            <img src="/logo.jpg" alt="Harneshwar Multispeciality Hospital" className="h-10 w-auto" />
            <span className="font-bold text-sm md:text-lg">Harneshwar Multispeciality Hospital</span>
          </Link>
          {isMobile ? (
            <MobileMenu />
          ) : (
            <NavigationMenu>
              <NavigationMenuList>
                <NavLinks />
              </NavigationMenuList>
            </NavigationMenu>
          )}
        </div>
      </div>
    </div>
  );
};

export default Navbar;
