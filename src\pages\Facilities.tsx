import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Building2, Bed, Microscope, Hospital, Siren, HeartPulse } from "lucide-react";

const facilities = [
  {
    name: "Emergency Medicine",
    description: "24/7 emergency care facility equipped with advanced life support systems and trauma care capabilities.",
    icon: Siren
  },
  {
    name: "Intensive Care Unit",
    description: "State-of-the-art ICU with continuous monitoring and specialized care for critical patients.",
    icon: HeartPulse
  },
  {
    name: "Diagnostic Imaging Center",
    description: "Advanced imaging services including X-ray, and ultrasound with rapid reporting.",
    icon: Microscope
  },
  {
    name: "Inpatient Wards",
    description: "Comfortable private and semi-private rooms with modern amenities for patient recovery.",
    icon: Bed
  },
  {
    name: "Rehabilitation Center",
    description: "Comprehensive rehabilitation services with physical, occupational, and speech therapy.",
    icon: Hospital
  },
  {
    name: "Outpatient Clinics",
    description: "Modern outpatient facilities for consultations, minor procedures, and follow-up care.",
    icon: Building2
  }
];

const Facilities = () => {
  return (
    <div className="container mx-auto px-4 py-8 animate-fade-up">
      <h1 className="text-4xl font-bold text-hospital-blue mb-8">Our Facilities</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {facilities.map((facility, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <facility.icon className="w-6 h-6 text-hospital-blue" />
                <CardTitle>{facility.name}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>{facility.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
      <br/>
      <br/>
      <p className="text-lg text-gray-700 mb-8">
            Call us @ +91-772-202-6440 / +91-932-288-7125
      </p>
    </div>
  );
};

export default Facilities;