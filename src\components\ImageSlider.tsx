import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import AutoPlay from "embla-carousel-autoplay";

const images = [
  {
    url: "/hospital/hospwebp2.webp",
    alt: "Medical Equipment",
  },
  {
    url: "/hospital/hosp3.jpg",
    alt: "Modern Hospital Building",
  },
  {
    url: "/hospital/hosp4.jpg",
    alt: "Medical Equipment",
  },
  {
    url: "/hospital/hospwebp1.webp",
    alt: "Medical Equipment",
  },
  {
    url: "/hospital/hospwebp3.webp",
    alt: "Medical Equipment",
  },
  {
    url: "/hospital/hosp5.jpg",
    alt: "Hospital Room",
  },
  {
    url: "/hospital/hosp8.jpg",
    alt: "Medical Staff",
  },
  {
    url: "/hospital/hosp9.jpg",
    alt: "Medical Staff",
  },
];

export const ImageSlider = () => {
  const plugin = AutoPlay({ delay: 4000, stopOnInteraction: true });

  return (
    <section className="py-12 bg-gray-400">
      <div className="container mx-auto px-4">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[plugin]}
          className="w-full max-w-5xl mx-auto"
        >
          <CarouselContent>
            {images.map((image, index) => (
              <CarouselItem key={index}>
                <AspectRatio ratio={16 / 9}>
                  <img
                    src={image.url}
                    alt={image.alt}
                    className="w-full h-full object-cover rounded-lg"
                  />
                </AspectRatio>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>
    </section>
  );
};