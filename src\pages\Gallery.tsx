import { AspectRatio } from "@/components/ui/aspect-ratio";

const images = [
  
  {
    url: "/hospital/hosp3.jpg",
    alt: "Medical Equipment",
    description: "The Hospital",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp4.jpg",
    alt: "Medical Equipment",
    description: "The Reception Area",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp5.jpg",
    alt: "Medical Equipment",
    description: "The Waiting Area",
    span: "col-span-2"
  },
  // {
  //   url: "/hospital/hosp6.jpg",
  //   alt: "Medical Equipment",
  //   description: "Advanced surgical equipment in our operation theaters",
  //   span: "col-span-2"
  // },
  {
    url: "/hospital/hosp7.jpg",
    alt: "Medical Equipment",
    description: "The Intensive Care Unit (ICU)",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp8.jpg",
    alt: "Medical Equipment",
    description: "The Operation Theater",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp9.jpg",
    alt: "Medical Equipment",
    description: "Professional healthcare team at your service",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp10.jpg",
    alt: "Medical Equipment",
    description: "Modern medical facilities for comprehensive care",
    span: "col-span-2"
  },
  // {
  //   url: "/hospital/hosp11.jpg",
  //   alt: "Medical Equipment",
  //   description: "Advanced diagnostic equipment for accurate results",
  //   span: "col-span-2"
  // },
  // {
  //   url: "/hospital/hosp14.jpg",
  //   alt: "Medical Equipment",
  //   description: "Well-equipped patient rooms for comfortable recovery",
  //   span: "col-span-2"
  // },
  // {
  //   url: "/hospital/hosp15.jpg",
  //   alt: "Medical Equipment",
  //   description: "Modern medical infrastructure for better healthcare",
  //   span: "col-span-2"
  // },
  {
    url: "/hospital/hosp16.jpg",
    alt: "Medical Equipment",
    description: "Advanced diagnostic equipment for accurate results",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp17.jpg",
    alt: "Medical Equipment",
    description: "Laboratory facilities",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp18.jpg",
    alt: "Medical Equipment",
    description: "Advanced healthcare equipment for better patient care",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp19.jpg",
    alt: "Medical Equipment",
    description: "Medical Store",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp20.jpg",
    alt: "Medical Equipment",
    description: "Well-equipped medical facilities",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp1.jpg",
    alt: "Modern Hospital Building",
    description: "Our hospital building at the very beginning",
    span: "col-span-2"
  },
  {
    url: "/hospital/hosp2.jpg",
    alt: "Medical Equipment",
    description: "The hospital a few years ago",
    span: "col-span-2"
  },
];

const Gallery = () => {
  return (
    <div className="container mx-auto px-4 py-8 animate-fade-up">
      <h1 className="text-4xl font-bold text-hospital-blue mb-8">Gallery</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {images.map((image, index) => (
          <div
            key={index}
            className={`${image.span || ""} overflow-hidden rounded-lg shadow-lg group relative`}
          >
            <AspectRatio ratio={16 / 9} className="bg-gray-100">
              <img
                src={image.url}
                alt={image.alt}
                className="w-full h-full object-cover transition-all duration-300 group-hover:brightness-50 group-hover:scale-105 "
                loading="lazy"
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-white text-center px-4 text-sm md:text-base">
                  {image.description}
                </p>
              </div>
            </AspectRatio>
          </div>
        ))}
      </div>
      <br/>
      <br/>
      <p className="text-lg text-gray-700 mb-8">
            Call us @ +91-772-202-6440 / +91-932-288-7125
      </p>
    </div>
  );
};

export default Gallery;