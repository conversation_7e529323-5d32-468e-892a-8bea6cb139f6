// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xlaigngttsvaxqlsdvuz.supabase.co";
// Is this really publishable?
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsYWlnbmd0dHN2YXhxbHNkdnV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzcwMjg2OTMsImV4cCI6MjA1MjYwNDY5M30.K_l_r7EqkYXQ8vwTvrhc-jFD8uguBp4EUiS_5-yOCbU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);