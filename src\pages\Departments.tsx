import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>eth<PERSON><PERSON>, <PERSON>, <PERSON>, Baby, Bone, Syringe } from "lucide-react";

const departments = [
  {
    name: "Cardiology",
    description: "State-of-the-art cardiac care facility offering comprehensive heart disease diagnosis, treatment, and prevention services.",
    icon: Heart
  },
  {
    name: "Neurology",
    description: "Expert care for disorders of the nervous system, brain, and spine with advanced diagnostic and therapeutic capabilities.",
    icon: Brain
  },
  {
    name: "Pediatrics",
    description: "Specialized healthcare for infants, children, and adolescents in a child-friendly environment.",
    icon: Baby
  },
  {
    name: "Orthopedics",
    description: "Complete care for musculoskeletal conditions, from sports injuries to joint replacements.",
    icon: Bone
  },
  {
    name: "Internal Medicine",
    description: "Comprehensive adult healthcare covering prevention, diagnosis, and treatment of various diseases.",
    icon: Stethoscope
  },
  {
    name: "Diagnostic Services",
    description: "Advanced diagnostic testing and laboratory services with quick and accurate results.",
    icon: Syringe
  }
];

const Departments = () => {
  return (
    <div className="container mx-auto px-4 py-8 animate-fade-up">
      <h1 className="text-4xl font-bold text-hospital-blue mb-8">Our Departments</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {departments.map((dept, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <dept.icon className="w-6 h-6 text-hospital-blue" />
                <CardTitle>{dept.name}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>{dept.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
      <br/>
      <br/>
      <p className="text-lg text-gray-700 mb-8">
            Call us @ +91-772-202-6440 / +91-932-288-7125
      </p>
    </div>
  );
};

export default Departments;